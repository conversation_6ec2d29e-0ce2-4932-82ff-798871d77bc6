# qmt_trader/utils/constants.py
"""
常量定义模块
包含交易系统中使用的各种常量
"""

from xtquant import xtconstant

# 交易类型映射
ORDER_TYPE_MAPPING = {
    'buy': xtconstant.STOCK_BUY,
    'sell': xtconstant.STOCK_SELL
}

# 股票交易所映射
STOCK_EXCHANGE_MAP = {
    '60': '.SH',    # 上海主板
    '68': '.SH',    # 科创板
    '00': '.SZ',    # 深圳主板
    '30': '.SZ',    # 创业板
    '83': '.BJ'     # 北交所
}

# 证券类型识别规则
SECURITY_TYPE_RULES = {
    'bond': {
        'prefixes_3': ['110', '113', '123', '127', '128', '111', '118'],
        'prefixes_2': ['11', '12']
    },
    'fund': {
        'prefixes_3': ['510', '511', '512', '513', '514', '515', '516', '517', '518', '588', '159', '501', '164'],
        'prefixes_2': ['16']
    }
}

# 交易时间常量
TRADING_TIME_CONFIG = {
    'WEEKDAYS': 4,          # 交易日（周一到周五，0-4）
    'MORNING_START': 9,     # 上午开始时间
    'MORNING_END': 11,      # 上午结束时间
    'AFTERNOON_START': 13,  # 下午开始时间
    'AFTERNOON_END': 15,    # 下午结束时间
    'MORNING_START_MIN': 30,    # 上午开始分钟
    'MORNING_END_MIN': 30,      # 上午结束分钟
    'AFTERNOON_START_MIN': 0,   # 下午开始分钟
    'AFTERNOON_END_MIN': 0,     # 下午结束分钟
    'CALL_AUCTION_MIN': 15      # 集合竞价开始分钟
}

# 价格精度常量
PRICE_PRECISION = {
    'STOCK': 3,     # 股票价格保留3位小数
    'BOND': 3,      # 债券价格保留3位小数
    'FUND': 3       # 基金价格保留3位小数
}

# 滑点调整系数
SLIPPAGE_FACTOR = {
    'STOCK': 1.0,       # 股票滑点系数
    'BOND': 0.1,        # 债券滑点系数（较小）
    'FUND': 0.1         # 基金滑点系数（较小）
}

# 默认配置常量
DEFAULT_SLIPPAGE = 0.01     # 默认滑点
DEFAULT_SESSION_ID_RANGE = (10000000, 99999999)  # 会话ID范围

# 数据库配置常量
DEFAULT_DATABASE_CONFIG = {
    'type': 'sqlite',           # 数据库类型
    'path': 'qmt_trader.db',    # SQLite数据库文件路径
    'host': 'localhost',        # MySQL/PostgreSQL主机
    'port': 3306,               # MySQL端口 (PostgreSQL: 5432)
    'user': '',                 # 用户名
    'password': '',             # 密码
    'database': 'qmt_trader',   # 数据库名
    'enable_storage': True      # 是否启用数据库存储
}
