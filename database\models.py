# qmt_trader/database/models.py
"""
数据库模型定义
定义各种数据表的结构和字段
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from decimal import Decimal


@dataclass
class MarketDataModel:
    """行情数据模型"""
    stock_code: str
    period: str
    timestamp: datetime
    open_price: Optional[Decimal] = None
    high_price: Optional[Decimal] = None
    low_price: Optional[Decimal] = None
    close_price: Optional[Decimal] = None
    volume: Optional[int] = None
    amount: Optional[Decimal] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class TradeOrderModel:
    """交易委托模型"""
    stock_code: str
    order_id: str
    account_type: Optional[str] = None
    account_id: Optional[str] = None
    order_sysid: Optional[str] = None
    order_time: Optional[datetime] = None
    order_type: Optional[int] = None
    order_volume: Optional[int] = None
    price_type: Optional[int] = None
    order_price: Optional[Decimal] = None
    traded_volume: Optional[int] = None
    traded_price: Optional[Decimal] = None
    order_status: Optional[int] = None
    status_msg: Optional[str] = None
    strategy_name: Optional[str] = None
    order_remark: Optional[str] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class TradeRecordModel:
    """成交记录模型"""
    stock_code: str
    account_type: Optional[str] = None
    account_id: Optional[str] = None
    trade_id: Optional[str] = None
    order_id: Optional[str] = None
    trade_time: Optional[datetime] = None
    trade_volume: Optional[int] = None
    trade_price: Optional[Decimal] = None
    trade_amount: Optional[Decimal] = None
    trade_type: Optional[int] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None


@dataclass
class PositionModel:
    """持仓记录模型"""
    account_id: str
    stock_code: str
    record_date: datetime
    account_type: Optional[str] = None
    volume: Optional[int] = None
    can_use_volume: Optional[int] = None
    open_price: Optional[Decimal] = None
    market_value: Optional[Decimal] = None
    record_time: Optional[datetime] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class AccountAssetModel:
    """资金记录模型"""
    account_id: str
    record_date: datetime
    account_type: Optional[str] = None
    cash: Optional[Decimal] = None
    frozen_cash: Optional[Decimal] = None
    market_value: Optional[Decimal] = None
    total_asset: Optional[Decimal] = None
    record_time: Optional[datetime] = None
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


# 表结构定义
TABLE_SCHEMAS = {
    'market_data': """
        CREATE TABLE IF NOT EXISTS market_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            stock_code VARCHAR(20) NOT NULL,
            period VARCHAR(10) NOT NULL,
            timestamp DATETIME NOT NULL,
            open_price DECIMAL(10,3),
            high_price DECIMAL(10,3),
            low_price DECIMAL(10,3),
            close_price DECIMAL(10,3),
            volume BIGINT,
            amount DECIMAL(15,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE UNIQUE INDEX IF NOT EXISTS uk_stock_period_time 
        ON market_data (stock_code, period, timestamp);
        CREATE INDEX IF NOT EXISTS idx_stock_code ON market_data (stock_code);
        CREATE INDEX IF NOT EXISTS idx_timestamp ON market_data (timestamp);
        CREATE INDEX IF NOT EXISTS idx_period ON market_data (period);
    """,
    
    'trade_orders': """
        CREATE TABLE IF NOT EXISTS trade_orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_type VARCHAR(20),
            account_id VARCHAR(50),
            stock_code VARCHAR(20) NOT NULL,
            order_id VARCHAR(50) UNIQUE,
            order_sysid VARCHAR(50),
            order_time DATETIME,
            order_type INTEGER,
            order_volume INTEGER,
            price_type INTEGER,
            order_price DECIMAL(10,3),
            traded_volume INTEGER DEFAULT 0,
            traded_price DECIMAL(10,3) DEFAULT 0,
            order_status INTEGER,
            status_msg VARCHAR(200),
            strategy_name VARCHAR(100),
            order_remark VARCHAR(200),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE INDEX IF NOT EXISTS idx_account_id ON trade_orders (account_id);
        CREATE INDEX IF NOT EXISTS idx_stock_code ON trade_orders (stock_code);
        CREATE INDEX IF NOT EXISTS idx_order_time ON trade_orders (order_time);
        CREATE INDEX IF NOT EXISTS idx_order_status ON trade_orders (order_status);
    """,
    
    'trade_records': """
        CREATE TABLE IF NOT EXISTS trade_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_type VARCHAR(20),
            account_id VARCHAR(50),
            stock_code VARCHAR(20) NOT NULL,
            trade_id VARCHAR(50),
            order_id VARCHAR(50),
            trade_time DATETIME,
            trade_volume INTEGER,
            trade_price DECIMAL(10,3),
            trade_amount DECIMAL(15,2),
            trade_type INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE INDEX IF NOT EXISTS idx_account_id ON trade_records (account_id);
        CREATE INDEX IF NOT EXISTS idx_stock_code ON trade_records (stock_code);
        CREATE INDEX IF NOT EXISTS idx_trade_time ON trade_records (trade_time);
        CREATE INDEX IF NOT EXISTS idx_order_id ON trade_records (order_id);
    """,
    
    'positions': """
        CREATE TABLE IF NOT EXISTS positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_type VARCHAR(20),
            account_id VARCHAR(50),
            stock_code VARCHAR(20) NOT NULL,
            volume INTEGER,
            can_use_volume INTEGER,
            open_price DECIMAL(10,3),
            market_value DECIMAL(15,2),
            record_date DATE,
            record_time DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE UNIQUE INDEX IF NOT EXISTS uk_account_stock_date 
        ON positions (account_id, stock_code, record_date);
        CREATE INDEX IF NOT EXISTS idx_account_id ON positions (account_id);
        CREATE INDEX IF NOT EXISTS idx_stock_code ON positions (stock_code);
        CREATE INDEX IF NOT EXISTS idx_record_date ON positions (record_date);
    """,
    
    'account_assets': """
        CREATE TABLE IF NOT EXISTS account_assets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_type VARCHAR(20),
            account_id VARCHAR(50),
            cash DECIMAL(15,2),
            frozen_cash DECIMAL(15,2),
            market_value DECIMAL(15,2),
            total_asset DECIMAL(15,2),
            record_date DATE,
            record_time DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE UNIQUE INDEX IF NOT EXISTS uk_account_date 
        ON account_assets (account_id, record_date);
        CREATE INDEX IF NOT EXISTS idx_account_id ON account_assets (account_id);
        CREATE INDEX IF NOT EXISTS idx_record_date ON account_assets (record_date);
    """
}
