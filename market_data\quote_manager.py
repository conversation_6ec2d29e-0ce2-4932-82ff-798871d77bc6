# qmt_trader/market_data/quote_manager.py
"""
行情监控模块
提供实时行情订阅和管理功能
"""

import logging
from typing import Callable, Optional, Dict, Any, List
from xtquant import xtdata
from ..utils import adjust_stock_code

logger = logging.getLogger(__name__)


class QuoteManager:
    """行情管理器"""
    
    def __init__(self):
        """初始化行情管理器"""
        self.subscribed_stocks: Dict[str, Dict] = {}
        self.quote_callbacks: Dict[str, List[Callable]] = {}
        self.is_running = False
        self.enable_database = True  # 默认启用数据库存储
        
    def subscribe_quote(
        self,
        stock_code: str,
        period: str = '1d',
        callback: Optional[Callable] = None
    ) -> bool:
        """
        订阅实时行情
        
        Args:
            stock_code: 股票代码
            period: 行情周期 ('1m', '5m', '15m', '30m', '1h', '1d' 等)
            callback: 行情回调函数
            
        Returns:
            订阅是否成功
        """
        try:
            # 标准化股票代码
            stock_code = adjust_stock_code(stock_code)
            
            # 使用默认回调如果没有提供
            if callback is None:
                callback = self.default_quote_callback
            
            # 记录订阅信息
            subscription_key = f"{stock_code}_{period}"
            self.subscribed_stocks[subscription_key] = {
                'stock_code': stock_code,
                'period': period,
                'callback': callback
            }
            
            # 添加到回调列表
            if subscription_key not in self.quote_callbacks:
                self.quote_callbacks[subscription_key] = []
            self.quote_callbacks[subscription_key].append(callback)
            
            logger.info(f"订阅行情: {stock_code} 周期={period}")
            
            # 订阅行情
            xtdata.subscribe_quote(
                stock_code=stock_code,
                period=period,
                callback=self._quote_callback_wrapper(subscription_key)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"订阅行情失败: {stock_code}, 错误: {str(e)}")
            return False
    
    def unsubscribe_quote(self, stock_code: str, period: str = '1d') -> bool:
        """
        取消订阅行情
        
        Args:
            stock_code: 股票代码
            period: 行情周期
            
        Returns:
            取消订阅是否成功
        """
        try:
            stock_code = adjust_stock_code(stock_code)
            subscription_key = f"{stock_code}_{period}"
            
            if subscription_key in self.subscribed_stocks:
                # 取消订阅
                xtdata.unsubscribe_quote(stock_code=stock_code, period=period)
                
                # 清理记录
                del self.subscribed_stocks[subscription_key]
                if subscription_key in self.quote_callbacks:
                    del self.quote_callbacks[subscription_key]
                
                logger.info(f"取消订阅行情: {stock_code} 周期={period}")
                return True
            else:
                logger.warning(f"未找到订阅记录: {stock_code} 周期={period}")
                return False
                
        except Exception as e:
            logger.error(f"取消订阅行情失败: {stock_code}, 错误: {str(e)}")
            return False
    
    def add_callback(self, stock_code: str, period: str, callback: Callable) -> bool:
        """
        为已订阅的行情添加额外的回调函数
        
        Args:
            stock_code: 股票代码
            period: 行情周期
            callback: 回调函数
            
        Returns:
            添加是否成功
        """
        try:
            stock_code = adjust_stock_code(stock_code)
            subscription_key = f"{stock_code}_{period}"
            
            if subscription_key in self.subscribed_stocks:
                if subscription_key not in self.quote_callbacks:
                    self.quote_callbacks[subscription_key] = []
                self.quote_callbacks[subscription_key].append(callback)
                logger.info(f"添加行情回调: {stock_code} 周期={period}")
                return True
            else:
                logger.warning(f"未找到订阅记录，无法添加回调: {stock_code} 周期={period}")
                return False
                
        except Exception as e:
            logger.error(f"添加行情回调失败: {str(e)}")
            return False
    
    def _quote_callback_wrapper(self, subscription_key: str):
        """
        行情回调包装器
        
        Args:
            subscription_key: 订阅键
            
        Returns:
            包装后的回调函数
        """
        def wrapper(datas):
            try:
                # 执行所有注册的回调函数
                if subscription_key in self.quote_callbacks:
                    for callback in self.quote_callbacks[subscription_key]:
                        try:
                            callback(datas)
                        except Exception as e:
                            logger.error(f"执行行情回调失败: {str(e)}")
            except Exception as e:
                logger.error(f"行情回调包装器异常: {str(e)}")
        
        return wrapper
    
    def default_quote_callback(self, datas):
        """
        默认行情回调函数

        Args:
            datas: 行情数据
        """
        logger.debug(f"行情更新: {datas}")

        # 保存行情数据到数据库
        if self.enable_database:
            self._save_quote_to_database(datas)

    def _save_quote_to_database(self, datas):
        """
        保存行情数据到数据库

        Args:
            datas: 行情数据
        """
        try:
            # 延迟导入避免循环依赖
            from ..database import market_data_storage, MarketDataModel
            from datetime import datetime
            from decimal import Decimal
            import pandas as pd

            if not isinstance(datas, dict):
                return

            for stock_code, stock_data in datas.items():
                if not isinstance(stock_data, pd.DataFrame):
                    continue

                # 获取该股票的订阅信息
                period = '1d'  # 默认周期
                for key, info in self.subscribed_stocks.items():
                    if stock_code in key:
                        period = info.get('period', '1d')
                        break

                # 保存最新的行情数据
                if not stock_data.empty:
                    latest_data = stock_data.iloc[-1]
                    timestamp = stock_data.index[-1] if hasattr(stock_data.index[-1], 'to_pydatetime') else datetime.now()

                    market_data = MarketDataModel(
                        stock_code=stock_code,
                        period=period,
                        timestamp=timestamp,
                        open_price=Decimal(str(latest_data.get('open', 0))) if pd.notna(latest_data.get('open')) else None,
                        high_price=Decimal(str(latest_data.get('high', 0))) if pd.notna(latest_data.get('high')) else None,
                        low_price=Decimal(str(latest_data.get('low', 0))) if pd.notna(latest_data.get('low')) else None,
                        close_price=Decimal(str(latest_data.get('close', 0))) if pd.notna(latest_data.get('close')) else None,
                        volume=int(latest_data.get('volume', 0)) if pd.notna(latest_data.get('volume')) else None
                    )

                    market_data_storage.save_market_data(market_data)
                    logger.debug(f"行情数据已保存到数据库: {stock_code}")

        except Exception as e:
            logger.error(f"保存行情数据到数据库失败: {str(e)}")

    def enable_database_storage(self, enable: bool = True):
        """
        启用或禁用数据库存储

        Args:
            enable: 是否启用数据库存储
        """
        self.enable_database = enable
        logger.info(f"行情数据库存储已{'启用' if enable else '禁用'}")

    def save_historical_data(self, stock_code: str, period: str = '1d'):
        """
        保存历史行情数据到数据库

        Args:
            stock_code: 股票代码
            period: 周期
        """
        try:
            from ..database import market_data_storage

            # 获取本地历史数据
            data = xtdata.get_local_data(
                field_list=["open", "high", "low", "close", "volume"],
                stock_list=[adjust_stock_code(stock_code)],
                period=period,
                start_time="",
                end_time=""
            )

            if data:
                success = market_data_storage.save_xtdata_result(
                    adjust_stock_code(stock_code), period, data
                )
                if success:
                    logger.info(f"历史数据保存成功: {stock_code} {period}")
                else:
                    logger.warning(f"历史数据保存失败: {stock_code} {period}")

        except Exception as e:
            logger.error(f"保存历史数据失败: {str(e)}")

    def get_historical_data(self, stock_code: str, period: str = '1d',
                           start_date=None, end_date=None, limit=None):
        """
        从数据库获取历史行情数据

        Args:
            stock_code: 股票代码
            period: 周期
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制条数

        Returns:
            历史行情数据DataFrame
        """
        try:
            from ..database import market_data_query

            return market_data_query.get_market_data(
                adjust_stock_code(stock_code),
                period,
                start_date,
                end_date,
                limit
            )

        except Exception as e:
            logger.error(f"查询历史数据失败: {str(e)}")
            return pd.DataFrame()
    
    def start_quote_service(self):
        """启动行情服务"""
        try:
            if not self.is_running:
                xtdata.run()
                self.is_running = True
                logger.info("行情服务已启动")
        except Exception as e:
            logger.error(f"启动行情服务失败: {str(e)}")
    
    def stop_quote_service(self):
        """停止行情服务"""
        try:
            if self.is_running:
                # 取消所有订阅
                for subscription_key, info in self.subscribed_stocks.items():
                    xtdata.unsubscribe_quote(
                        stock_code=info['stock_code'],
                        period=info['period']
                    )
                
                # 清理数据
                self.subscribed_stocks.clear()
                self.quote_callbacks.clear()
                self.is_running = False
                logger.info("行情服务已停止")
        except Exception as e:
            logger.error(f"停止行情服务失败: {str(e)}")
    
    def get_subscribed_stocks(self) -> Dict[str, Dict]:
        """
        获取已订阅的股票列表
        
        Returns:
            已订阅股票的字典
        """
        return self.subscribed_stocks.copy()
    
    def switch_quote_server(self, server_info: Dict[str, Any]) -> bool:
        """
        切换行情服务器
        
        Args:
            server_info: 服务器信息字典，包含 'ip' 和 'port'
            
        Returns:
            切换是否成功
        """
        try:
            logger.info(f"切换行情服务器: {server_info.get('ip')}:{server_info.get('port')}")
            
            # 创建行情服务器连接
            qs = xtdata.QuoteServer(server_info)
            result = qs.connect()

            if result.get("result", False):
                logger.info("行情服务器切换成功")
                return True
            else:
                logger.error(f"行情服务器切换失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"切换行情服务器异常: {str(e)}")
            return False


# 全局行情管理器实例
quote_manager = QuoteManager()


def subscribe_quote(stock_code: str, period: str = '1d', callback: Optional[Callable] = None) -> bool:
    """
    订阅实时行情（便捷函数）
    
    Args:
        stock_code: 股票代码
        period: 行情周期
        callback: 行情回调函数
        
    Returns:
        订阅是否成功
    """
    return quote_manager.subscribe_quote(stock_code, period, callback)


def unsubscribe_quote(stock_code: str, period: str = '1d') -> bool:
    """
    取消订阅行情（便捷函数）
    
    Args:
        stock_code: 股票代码
        period: 行情周期
        
    Returns:
        取消订阅是否成功
    """
    return quote_manager.unsubscribe_quote(stock_code, period)


def switch_quote_server(server_info: Dict[str, Any]) -> bool:
    """
    切换行情服务器（便捷函数）
    
    Args:
        server_info: 服务器信息
        
    Returns:
        切换是否成功
    """
    return quote_manager.switch_quote_server(server_info)
