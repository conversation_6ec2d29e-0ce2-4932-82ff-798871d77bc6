# qmt_trader/database/connection.py
"""
数据库连接管理模块
支持多种数据库类型：SQLite、MySQL、PostgreSQL等
"""

import logging
import sqlite3
import threading
from typing import Optional, Dict, Any, Union
from pathlib import Path
from contextlib import contextmanager

logger = logging.getLogger(__name__)

# 尝试导入可选的数据库驱动
try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    logger.debug("PyMySQL not available")

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False
    logger.debug("psycopg2 not available")


class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self._connections: Dict[str, Any] = {}
        self._lock = threading.Lock()
        self._config: Optional[Dict[str, Any]] = None
        
    def configure(self, config: Dict[str, Any]):
        """
        配置数据库连接参数
        
        Args:
            config: 数据库配置字典
                {
                    'type': 'sqlite|mysql|postgresql',
                    'database': 'database_name',
                    'host': 'localhost',  # MySQL/PostgreSQL
                    'port': 3306,         # MySQL/PostgreSQL
                    'user': 'username',   # MySQL/PostgreSQL
                    'password': 'password', # MySQL/PostgreSQL
                    'path': 'db_path'     # SQLite
                }
        """
        self._config = config
        logger.info(f"数据库配置已设置: {config.get('type', 'unknown')}")
    
    def get_connection(self, connection_name: str = 'default'):
        """
        获取数据库连接
        
        Args:
            connection_name: 连接名称
            
        Returns:
            数据库连接对象
        """
        with self._lock:
            if connection_name not in self._connections:
                self._connections[connection_name] = self._create_connection()
            
            return self._connections[connection_name]
    
    def _create_connection(self):
        """创建数据库连接"""
        if not self._config:
            # 默认使用SQLite
            self._config = {
                'type': 'sqlite',
                'path': 'qmt_trader.db'
            }
        
        db_type = self._config.get('type', 'sqlite').lower()
        
        if db_type == 'sqlite':
            return self._create_sqlite_connection()
        elif db_type == 'mysql':
            return self._create_mysql_connection()
        elif db_type == 'postgresql':
            return self._create_postgresql_connection()
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    def _create_sqlite_connection(self):
        """创建SQLite连接"""
        db_path = self._config.get('path', 'qmt_trader.db')
        
        # 确保目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(db_path, check_same_thread=False)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        
        logger.info(f"SQLite连接已创建: {db_path}")
        return conn
    
    def _create_mysql_connection(self):
        """创建MySQL连接"""
        if not MYSQL_AVAILABLE:
            raise ImportError("PyMySQL未安装，无法连接MySQL数据库")
        
        config = {
            'host': self._config.get('host', 'localhost'),
            'port': self._config.get('port', 3306),
            'user': self._config.get('user'),
            'password': self._config.get('password'),
            'database': self._config.get('database'),
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        conn = pymysql.connect(**config)
        logger.info(f"MySQL连接已创建: {config['host']}:{config['port']}")
        return conn
    
    def _create_postgresql_connection(self):
        """创建PostgreSQL连接"""
        if not POSTGRESQL_AVAILABLE:
            raise ImportError("psycopg2未安装，无法连接PostgreSQL数据库")
        
        config = {
            'host': self._config.get('host', 'localhost'),
            'port': self._config.get('port', 5432),
            'user': self._config.get('user'),
            'password': self._config.get('password'),
            'database': self._config.get('database')
        }
        
        conn = psycopg2.connect(**config)
        conn.autocommit = True
        logger.info(f"PostgreSQL连接已创建: {config['host']}:{config['port']}")
        return conn
    
    def close_connection(self, connection_name: str = 'default'):
        """
        关闭数据库连接

        Args:
            connection_name: 连接名称
        """
        with self._lock:
            if connection_name in self._connections:
                try:
                    conn = self._connections[connection_name]
                    if conn:
                        conn.close()
                    del self._connections[connection_name]
                    logger.info(f"数据库连接已关闭: {connection_name}")
                except Exception as e:
                    logger.error(f"关闭数据库连接失败: {str(e)}")
                    # 强制删除连接引用
                    try:
                        del self._connections[connection_name]
                    except:
                        pass
    
    def close_all_connections(self):
        """关闭所有数据库连接"""
        with self._lock:
            for name in list(self._connections.keys()):
                self.close_connection(name)
    
    @contextmanager
    def get_cursor(self, connection_name: str = 'default'):
        """
        获取数据库游标的上下文管理器
        
        Args:
            connection_name: 连接名称
            
        Yields:
            数据库游标
        """
        conn = self.get_connection(connection_name)
        cursor = conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    def execute_script(self, script: str, connection_name: str = 'default'):
        """
        执行SQL脚本
        
        Args:
            script: SQL脚本内容
            connection_name: 连接名称
        """
        try:
            with self.get_cursor(connection_name) as cursor:
                # 分割脚本为单独的语句
                statements = [stmt.strip() for stmt in script.split(';') if stmt.strip()]
                
                for statement in statements:
                    cursor.execute(statement)
                
                logger.info(f"SQL脚本执行成功，共 {len(statements)} 条语句")
                
        except Exception as e:
            logger.error(f"SQL脚本执行失败: {str(e)}")
            raise
    
    def init_tables(self):
        """初始化数据库表"""
        from .models import TABLE_SCHEMAS
        
        logger.info("开始初始化数据库表...")
        
        for table_name, schema in TABLE_SCHEMAS.items():
            try:
                self.execute_script(schema)
                logger.info(f"表 {table_name} 初始化成功")
            except Exception as e:
                logger.error(f"表 {table_name} 初始化失败: {str(e)}")
                raise
        
        logger.info("数据库表初始化完成")


# 全局数据库管理器实例
database_manager = DatabaseManager()


def get_connection(connection_name: str = 'default'):
    """
    获取数据库连接（便捷函数）
    
    Args:
        connection_name: 连接名称
        
    Returns:
        数据库连接对象
    """
    return database_manager.get_connection(connection_name)


def close_connection(connection_name: str = 'default'):
    """
    关闭数据库连接（便捷函数）
    
    Args:
        connection_name: 连接名称
    """
    database_manager.close_connection(connection_name)


def configure_database(config: Dict[str, Any]):
    """
    配置数据库（便捷函数）
    
    Args:
        config: 数据库配置字典
    """
    database_manager.configure(config)


def init_database():
    """
    初始化数据库（便捷函数）
    """
    database_manager.init_tables()
