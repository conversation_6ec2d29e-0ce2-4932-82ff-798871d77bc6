# qmt_trader/callbacks/trader_callback.py
"""
交易回调处理模块
处理QMT交易系统的各种回调事件
"""

import logging
from typing import Optional, Callable, Any
from xtquant.xttrader import XtQuantTraderCallback

logger = logging.getLogger(__name__)


class QMTTraderCallback(XtQuantTraderCallback):
    """QMT交易回调处理类"""

    def __init__(self, trader_instance=None):
        """
        初始化回调处理器
        
        Args:
            trader_instance: 交易器实例，用于重连等操作
        """
        super().__init__()
        self.trader = trader_instance
        
        # 自定义回调函数
        self._on_disconnected_callback: Optional[Callable] = None
        self._on_order_callback: Optional[Callable] = None
        self._on_asset_callback: Optional[Callable] = None
        self._on_trade_callback: Optional[Callable] = None
        self._on_position_callback: Optional[Callable] = None
        self._on_order_error_callback: Optional[Callable] = None
        self._on_cancel_error_callback: Optional[Callable] = None

    def set_disconnected_callback(self, callback: Callable):
        """设置连接断开回调函数"""
        self._on_disconnected_callback = callback

    def set_order_callback(self, callback: Callable):
        """设置委托回报回调函数"""
        self._on_order_callback = callback

    def set_asset_callback(self, callback: Callable):
        """设置资金变动回调函数"""
        self._on_asset_callback = callback

    def set_trade_callback(self, callback: Callable):
        """设置成交回报回调函数"""
        self._on_trade_callback = callback

    def set_position_callback(self, callback: Callable):
        """设置持仓变动回调函数"""
        self._on_position_callback = callback

    def set_order_error_callback(self, callback: Callable):
        """设置委托失败回调函数"""
        self._on_order_error_callback = callback

    def set_cancel_error_callback(self, callback: Callable):
        """设置撤单失败回调函数"""
        self._on_cancel_error_callback = callback

    def on_disconnected(self):
        """连接断开回调"""
        logger.error("QMT连接断开，尝试重新连接...")
        
        # 执行自定义回调
        if self._on_disconnected_callback:
            try:
                self._on_disconnected_callback()
            except Exception as e:
                logger.error(f"执行自定义断开连接回调失败: {str(e)}")
        
        # 尝试重连
        if self.trader and hasattr(self.trader, 'reconnect'):
            try:
                self.trader.reconnect()
            except Exception as e:
                logger.error(f"自动重连失败: {str(e)}")

    def on_stock_order(self, order):
        """委托回报推送"""
        order_info = {
            'stock_code': order.stock_code,
            'order_status': order.order_status,
            'order_sysid': order.order_sysid,
            'order_id': order.order_id,
            'order_type': order.order_type,
            'order_volume': order.order_volume,
            'price': order.price,
            'traded_volume': order.traded_volume,
            'traded_price': order.traded_price,
            'order_time': order.order_time,
            'strategy_name': getattr(order, 'strategy_name', ''),
            'order_remark': getattr(order, 'order_remark', '')
        }
        
        logger.info(f"委托更新: {order.stock_code} 状态={order.order_status} 系统ID={order.order_sysid}")
        
        # 执行自定义回调
        if self._on_order_callback:
            try:
                self._on_order_callback(order_info)
            except Exception as e:
                logger.error(f"执行自定义委托回调失败: {str(e)}")

    def on_stock_asset(self, asset):
        """资金变动推送"""
        asset_info = {
            'account_id': asset.account_id,
            'account_type': asset.account_type,
            'cash': asset.cash,
            'frozen_cash': asset.frozen_cash,
            'market_value': asset.market_value,
            'total_asset': asset.total_asset
        }
        
        logger.info(f"资产更新: 账号={asset.account_id} 现金={asset.cash} 总资产={asset.total_asset}")
        
        # 执行自定义回调
        if self._on_asset_callback:
            try:
                self._on_asset_callback(asset_info)
            except Exception as e:
                logger.error(f"执行自定义资产回调失败: {str(e)}")

    def on_stock_trade(self, trade):
        """成交变动推送"""
        trade_info = {
            'stock_code': trade.stock_code,
            'traded_volume': trade.traded_volume,
            'traded_price': trade.traded_price,
            'traded_time': trade.traded_time,
            'order_id': trade.order_id,
            'trade_id': getattr(trade, 'trade_id', ''),
            'trade_type': getattr(trade, 'trade_type', '')
        }
        
        logger.info(f"成交: {trade.stock_code} 数量={trade.traded_volume} 价格={trade.traded_price}")
        
        # 执行自定义回调
        if self._on_trade_callback:
            try:
                self._on_trade_callback(trade_info)
            except Exception as e:
                logger.error(f"执行自定义成交回调失败: {str(e)}")

    def on_stock_position(self, position):
        """持仓变动推送"""
        position_info = {
            'stock_code': position.stock_code,
            'volume': position.volume,
            'can_use_volume': position.can_use_volume,
            'open_price': position.open_price,
            'market_value': position.market_value,
            'account_id': position.account_id,
            'account_type': position.account_type
        }
        
        logger.info(f"持仓更新: {position.stock_code} 数量={position.volume}")
        
        # 执行自定义回调
        if self._on_position_callback:
            try:
                self._on_position_callback(position_info)
            except Exception as e:
                logger.error(f"执行自定义持仓回调失败: {str(e)}")

    def on_order_error(self, order_error):
        """委托失败推送"""
        error_info = {
            'order_id': order_error.order_id,
            'error_id': order_error.error_id,
            'error_msg': order_error.error_msg,
            'stock_code': getattr(order_error, 'stock_code', ''),
            'order_type': getattr(order_error, 'order_type', ''),
            'order_volume': getattr(order_error, 'order_volume', 0),
            'price': getattr(order_error, 'price', 0.0)
        }
        
        logger.error(f"委托失败: ID={order_error.order_id} 错误码={order_error.error_id} 信息={order_error.error_msg}")
        
        # 执行自定义回调
        if self._on_order_error_callback:
            try:
                self._on_order_error_callback(error_info)
            except Exception as e:
                logger.error(f"执行自定义委托失败回调失败: {str(e)}")

    def on_cancel_error(self, cancel_error):
        """撤单失败推送"""
        error_info = {
            'order_id': cancel_error.order_id,
            'error_id': cancel_error.error_id,
            'error_msg': cancel_error.error_msg,
            'stock_code': getattr(cancel_error, 'stock_code', ''),
        }
        
        logger.error(f"撤单失败: ID={cancel_error.order_id} 错误码={cancel_error.error_id} 信息={cancel_error.error_msg}")
        
        # 执行自定义回调
        if self._on_cancel_error_callback:
            try:
                self._on_cancel_error_callback(error_info)
            except Exception as e:
                logger.error(f"执行自定义撤单失败回调失败: {str(e)}")


# 向后兼容的别名
MyXtQuantTraderCallback = QMTTraderCallback
