# qmt_trader/utils/time_utils.py
"""
时间处理工具模块
提供时间转换和交易时间验证功能
"""

import time
import logging
from typing import Tuple
from .constants import TRADING_TIME_CONFIG

logger = logging.getLogger(__name__)


def conv_time(ct: int) -> str:
    """
    将时间戳转换为格式化时间字符串
    
    Args:
        ct: 时间戳（毫秒）
        
    Returns:
        格式化的时间字符串，格式为 'YYYYMMDDHHMMSS.sss'
        
    Example:
        conv_time(1476374400000) --> '20161014000000.000'
    """
    try:
        local_time = time.localtime(ct / 1000)
        data_head = time.strftime('%Y%m%d%H%M%S', local_time)
        data_secs = (ct - int(ct)) * 1000
        time_stamp = f'{data_head}.{int(data_secs):03d}'
        return time_stamp
    except Exception as e:
        logger.error(f"时间转换失败: {ct}, 错误: {str(e)}")
        return ""


def is_trading_time(
        trader_days: int = None,
        start_hour: int = None,
        end_hour: int = None,
        start_min: int = None,
        jhjj: bool = False
) -> bool:
    """
    验证当前是否为交易时间
    
    Args:
        trader_days: 交易日范围（0-4表示周一到周五），默认使用配置
        start_hour: 开始小时，默认使用配置
        end_hour: 结束小时，默认使用配置
        start_min: 开始分钟，默认使用配置
        jhjj: 是否包含集合竞价时间（9:15-9:30）
        
    Returns:
        是否为交易时间
    """
    # 使用默认配置
    trader_days = trader_days if trader_days is not None else TRADING_TIME_CONFIG['WEEKDAYS']
    start_hour = start_hour if start_hour is not None else TRADING_TIME_CONFIG['MORNING_START']
    end_hour = end_hour if end_hour is not None else TRADING_TIME_CONFIG['AFTERNOON_END']
    start_min = start_min if start_min is not None else TRADING_TIME_CONFIG['MORNING_START_MIN']
    
    jhjj_time = TRADING_TIME_CONFIG['CALL_AUCTION_MIN'] if jhjj else start_min
    now = time.localtime()
    weekday = now.tm_wday

    # 检查是否为交易日
    if weekday > trader_days:  # 周末
        logger.debug("非交易日（周末）")
        return False

    hour, minute = now.tm_hour, now.tm_min

    # 检查交易时段
    if start_hour <= hour <= end_hour:
        # 上午开盘时间检查
        if hour == TRADING_TIME_CONFIG['MORNING_START']:
            return minute >= jhjj_time
        # 午间休市检查
        elif hour == TRADING_TIME_CONFIG['MORNING_END'] and minute > TRADING_TIME_CONFIG['MORNING_END_MIN']:
            return False
        # 下午开盘检查
        elif hour == 12:
            return False  # 12点整个小时都是休市
        elif hour == TRADING_TIME_CONFIG['AFTERNOON_START'] and minute < TRADING_TIME_CONFIG['AFTERNOON_START_MIN']:
            return False
        # 下午收盘检查
        elif hour == end_hour and minute >= start_min:
            return False
        return True
    
    return False


def get_current_time_info() -> dict:
    """
    获取当前时间信息
    
    Returns:
        包含当前时间详细信息的字典
    """
    now = time.localtime()
    return {
        'timestamp': time.time(),
        'year': now.tm_year,
        'month': now.tm_mon,
        'day': now.tm_mday,
        'hour': now.tm_hour,
        'minute': now.tm_min,
        'second': now.tm_sec,
        'weekday': now.tm_wday,
        'is_trading_time': is_trading_time(),
        'formatted_time': time.strftime('%Y-%m-%d %H:%M:%S', now)
    }


def get_trading_sessions() -> list:
    """
    获取交易时段信息
    
    Returns:
        交易时段列表
    """
    return [
        {
            'name': '集合竞价',
            'start_time': f"{TRADING_TIME_CONFIG['MORNING_START']}:{TRADING_TIME_CONFIG['CALL_AUCTION_MIN']:02d}",
            'end_time': f"{TRADING_TIME_CONFIG['MORNING_START']}:{TRADING_TIME_CONFIG['MORNING_START_MIN']:02d}",
            'description': '开盘集合竞价时间'
        },
        {
            'name': '上午交易',
            'start_time': f"{TRADING_TIME_CONFIG['MORNING_START']}:{TRADING_TIME_CONFIG['MORNING_START_MIN']:02d}",
            'end_time': f"{TRADING_TIME_CONFIG['MORNING_END']}:{TRADING_TIME_CONFIG['MORNING_END_MIN']:02d}",
            'description': '上午连续交易时间'
        },
        {
            'name': '下午交易',
            'start_time': f"{TRADING_TIME_CONFIG['AFTERNOON_START']}:{TRADING_TIME_CONFIG['AFTERNOON_START_MIN']:02d}",
            'end_time': f"{TRADING_TIME_CONFIG['AFTERNOON_END']}:{TRADING_TIME_CONFIG['AFTERNOON_END_MIN']:02d}",
            'description': '下午连续交易时间'
        }
    ]
