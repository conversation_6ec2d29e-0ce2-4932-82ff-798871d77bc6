# qmt_trader/trader/core.py
"""
核心交易功能模块
提供QMT交易的核心功能实现
"""

import logging
import time
import random
import pandas as pd
from typing import Tuple, Union, Optional
from xtquant.xttrader import XtQuantTrader
from xtquant.xttype import StockAccount
from xtquant import xtconstant

from ..config import load_config, DEFAULT_CONFIG
from ..utils import (
    adjust_stock_code,
    apply_slippage,
    is_trading_time,
    conv_time,
    DEFAULT_SESSION_ID_RANGE
)
from ..callbacks import QMTTraderCallback
from ..market_data import QuoteManager
from ..database import (
    trading_data_storage,
    trading_data_query,
    TradeOrderModel,
    TradeRecordModel,
    PositionModel,
    AccountAssetModel
)

logger = logging.getLogger(__name__)


class QMTTrader:
    """QMT交易接口核心类"""

    def __init__(
            self,
            path: Optional[str] = None,
            session_id: Optional[int] = None,
            account: Optional[str] = None,
            account_type: str = 'STOCK',
            is_slippage: bool = True,
            slippage: float = 0.01
    ) -> None:
        """
        初始化QMT交易器
        
        Args:
            path: QMT交易端路径
            session_id: 会话ID
            account: 交易账户
            account_type: 账户类型
            is_slippage: 是否启用滑点
            slippage: 滑点大小
        """
        # 加载配置
        config = load_config()
        
        # 初始化参数
        self.path = path or config.get('trader_path', DEFAULT_CONFIG['trader_path'])
        self.account = account or config.get('account', DEFAULT_CONFIG['account'])
        self.session_id = session_id or self._generate_session_id()
        self.account_type = account_type
        self.slippage = slippage if is_slippage else 0.0
        
        # 交易相关对象
        self.xt_trader: Optional[XtQuantTrader] = None
        self.acc: Optional[StockAccount] = None
        self.callback: Optional[QMTTraderCallback] = None
        self.connected = False
        
        # 行情管理器
        self.quote_manager = QuoteManager()

        # 数据库存储开关
        self.enable_database = True

        logger.info(f"QMT交易器初始化完成: 账户={self.account}, 会话ID={self.session_id}")

    def _generate_session_id(self) -> int:
        """生成随机会话ID"""
        return random.randint(*DEFAULT_SESSION_ID_RANGE)

    def connect(self) -> Tuple[XtQuantTrader, StockAccount]:
        """
        建立交易连接
        
        Returns:
            交易器和账户对象的元组
            
        Raises:
            ConnectionError: 连接失败时抛出
        """
        if self.connected and self.xt_trader and self.acc:
            logger.info("已存在有效连接")
            return self.xt_trader, self.acc

        logger.info(f"连接QMT: path={self.path} session={self.session_id}")
        
        try:
            # 创建交易器和账户对象
            self.xt_trader = XtQuantTrader(self.path, self.session_id)
            self.acc = StockAccount(self.account, self.account_type)
            self.callback = QMTTraderCallback(self)

            # 注册回调并启动
            self.xt_trader.register_callback(self.callback)
            self.xt_trader.start()
            
            # 连接交易服务器
            connect_result = self.xt_trader.connect()

            if connect_result == 0:
                # 订阅账户信息
                subscribe_result = self.xt_trader.subscribe(self.acc)
                logger.info(f"连接成功 订阅结果={subscribe_result}")
                self.connected = True
                return self.xt_trader, self.acc
            else:
                logger.error(f"连接失败 返回码={connect_result}")
                raise ConnectionError(f"QMT连接失败，返回码: {connect_result}")
                
        except Exception as e:
            logger.error(f"连接异常: {str(e)}")
            self.connected = False
            raise

    def reconnect(self) -> bool:
        """
        重新连接
        
        Returns:
            重连是否成功
        """
        logger.warning("执行重新连接...")
        try:
            # 断开现有连接
            if self.xt_trader:
                self.xt_trader.stop()
            
            self.connected = False
            time.sleep(1)
            
            # 重新连接
            self.connect()
            return True
            
        except Exception as e:
            logger.error(f"重连失败: {str(e)}")
            return False

    def disconnect(self):
        """断开连接"""
        try:
            if self.xt_trader:
                self.xt_trader.stop()
            self.connected = False
            logger.info("已断开QMT连接")
        except Exception as e:
            logger.error(f"断开连接异常: {str(e)}")

    def order_stock(
            self,
            stock_code: str,
            order_type: int,
            order_volume: int,
            price_type: int = xtconstant.FIX_PRICE,
            price: float = 0.0,
            strategy_name: str = '',
            order_remark: str = ''
    ) -> int:
        """
        统一委托接口
        
        Args:
            stock_code: 股票代码
            order_type: 委托类型
            order_volume: 委托数量
            price_type: 价格类型
            price: 委托价格
            strategy_name: 策略名称
            order_remark: 委托备注
            
        Returns:
            委托ID，失败返回-1
        """
        # 确保连接
        if not self.connected:
            self.connect()

        # 标准化股票代码
        stock_code = adjust_stock_code(stock_code)
        
        # 应用滑点调整
        adjusted_price = apply_slippage(stock_code, price, order_type, self.slippage)

        try:
            order_id = self.xt_trader.order_stock(
                account=self.acc,
                stock_code=stock_code,
                order_type=order_type,
                order_volume=order_volume,
                price_type=price_type,
                price=adjusted_price,
                strategy_name=strategy_name,
                order_remark=order_remark
            )
            
            logger.info(f"委托: {stock_code} 类型={order_type} 价={adjusted_price} 量={order_volume} ID={order_id}")

            # 保存委托数据到数据库
            if self.enable_database and order_id > 0:
                self._save_order_to_database(
                    stock_code, order_type, order_volume, adjusted_price,
                    order_id, strategy_name, order_remark
                )

            return order_id

        except Exception as e:
            logger.error(f"委托异常: {str(e)}")
            return -1

    def buy(
            self,
            security: str,
            amount: int,
            price: float = 0.0,
            price_type: int = xtconstant.FIX_PRICE,
            strategy_name: str = '',
            order_remark: str = ''
    ) -> int:
        """
        买入操作
        
        Args:
            security: 证券代码
            amount: 买入数量
            price: 买入价格
            price_type: 价格类型
            strategy_name: 策略名称
            order_remark: 委托备注
            
        Returns:
            委托ID
        """
        return self.order_stock(
            stock_code=security,
            order_type=xtconstant.STOCK_BUY,
            order_volume=amount,
            price_type=price_type,
            price=price,
            strategy_name=strategy_name,
            order_remark=order_remark
        )

    def sell(
            self,
            security: str,
            amount: int,
            price: float = 0.0,
            price_type: int = xtconstant.FIX_PRICE,
            strategy_name: str = '',
            order_remark: str = ''
    ) -> int:
        """
        卖出操作
        
        Args:
            security: 证券代码
            amount: 卖出数量
            price: 卖出价格
            price_type: 价格类型
            strategy_name: 策略名称
            order_remark: 委托备注
            
        Returns:
            委托ID
        """
        return self.order_stock(
            stock_code=security,
            order_type=xtconstant.STOCK_SELL,
            order_volume=amount,
            price_type=price_type,
            price=price,
            strategy_name=strategy_name,
            order_remark=order_remark
        )

    def cancel_order(self, order_id: int) -> bool:
        """
        撤销委托
        
        Args:
            order_id: 委托ID
            
        Returns:
            撤销是否成功
        """
        try:
            if not self.connected:
                self.connect()
                
            result = self.xt_trader.cancel_order_stock(self.acc, order_id)
            logger.info(f"撤销委托: ID={order_id} 结果={result}")
            return result == 0
            
        except Exception as e:
            logger.error(f"撤销委托异常: {str(e)}")
            return False

    def balance(self) -> pd.DataFrame:
        """
        查询账户资金

        Returns:
            资金信息DataFrame
        """
        try:
            if not self.connected:
                self.connect()

            asset = self.xt_trader.query_stock_asset(self.acc)
            if asset:
                asset_data = {
                    '账号类型': asset.account_type,
                    '资金账户': asset.account_id,
                    '可用金额': asset.cash,
                    '冻结金额': asset.frozen_cash,
                    '持仓市值': asset.market_value,
                    '总资产': asset.total_asset
                }

                # 保存到数据库
                if self.enable_database:
                    self._save_balance_to_database(asset_data)

                return pd.DataFrame([asset_data])
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"查询资金异常: {str(e)}")
            return pd.DataFrame()

    def position(self) -> pd.DataFrame:
        """
        查询持仓

        Returns:
            持仓信息DataFrame
        """
        try:
            if not self.connected:
                self.connect()

            positions = self.xt_trader.query_stock_positions(self.acc)
            if not positions:
                return pd.DataFrame(columns=[
                    '账号类型', '资金账号', '证券代码',
                    '股票余额', '可用余额', '成本价',
                    '参考成本价', '市值'
                ])

            data = []
            for pos in positions:
                data.append({
                    '账号类型': pos.account_type,
                    '资金账号': pos.account_id,
                    '证券代码': adjust_stock_code(pos.stock_code),
                    '股票余额': pos.volume,
                    '可用余额': pos.can_use_volume,
                    '成本价': pos.open_price,
                    '参考成本价': pos.open_price,
                    '市值': pos.market_value
                })

            df = pd.DataFrame(data)

            # 保存到数据库
            if self.enable_database and not df.empty:
                self._save_positions_to_database(df)

            return df

        except Exception as e:
            logger.error(f"查询持仓异常: {str(e)}")
            return pd.DataFrame()

    def today_entrusts(self) -> pd.DataFrame:
        """
        查询当日委托

        Returns:
            委托信息DataFrame
        """
        try:
            if not self.connected:
                self.connect()

            orders = self.xt_trader.query_stock_orders(self.acc)
            if not orders:
                return pd.DataFrame(columns=[
                    '账号类型', '资金账号', '证券代码', '订单编号',
                    '柜台合同编号', '报单时间', '委托类型', '委托数量',
                    '报价类型', '委托价格', '成交数量', '成交均价',
                    '委托状态', '委托状态描述', '策略名称', '委托备注'
                ])

            data = []
            for order in orders:
                data.append({
                    '账号类型': order.account_type,
                    '资金账号': order.account_id,
                    '证券代码': adjust_stock_code(order.stock_code),
                    '订单编号': order.order_id,
                    '柜台合同编号': order.order_sysid,
                    '报单时间': conv_time(order.order_time),
                    '委托类型': order.order_type,
                    '委托数量': order.order_volume,
                    '报价类型': order.price_type,
                    '委托价格': order.price,
                    '成交数量': order.traded_volume,
                    '成交均价': order.traded_price,
                    '委托状态': order.order_status,
                    '委托状态描述': order.status_msg,
                    '策略名称': order.strategy_name,
                    '委托备注': order.order_remark
                })
            return pd.DataFrame(data)

        except Exception as e:
            logger.error(f"查询委托异常: {str(e)}")
            return pd.DataFrame()

    def today_trades(self) -> pd.DataFrame:
        """
        查询当日成交

        Returns:
            成交信息DataFrame
        """
        try:
            if not self.connected:
                self.connect()

            trades = self.xt_trader.query_stock_trades(self.acc)
            if not trades:
                return pd.DataFrame(columns=[
                    '账号类型', '资金账号', '证券代码', '成交编号',
                    '订单编号', '成交时间', '成交数量', '成交价格',
                    '成交金额', '委托类型'
                ])

            data = []
            for trade in trades:
                data.append({
                    '账号类型': trade.account_type,
                    '资金账号': trade.account_id,
                    '证券代码': adjust_stock_code(trade.stock_code),
                    '成交编号': getattr(trade, 'trade_id', ''),
                    '订单编号': trade.order_id,
                    '成交时间': conv_time(trade.traded_time),
                    '成交数量': trade.traded_volume,
                    '成交价格': trade.traded_price,
                    '成交金额': trade.traded_volume * trade.traded_price,
                    '委托类型': getattr(trade, 'order_type', '')
                })
            return pd.DataFrame(data)

        except Exception as e:
            logger.error(f"查询成交异常: {str(e)}")
            return pd.DataFrame()

    # 行情相关方法
    def subscribe_quote(
            self,
            stock_code: str,
            period: str = '1d',
            callback: callable = None
    ) -> bool:
        """
        订阅实时行情

        Args:
            stock_code: 股票代码
            period: 行情周期
            callback: 行情回调函数

        Returns:
            订阅是否成功
        """
        return self.quote_manager.subscribe_quote(stock_code, period, callback)

    def unsubscribe_quote(self, stock_code: str, period: str = '1d') -> bool:
        """
        取消订阅行情

        Args:
            stock_code: 股票代码
            period: 行情周期

        Returns:
            取消订阅是否成功
        """
        return self.quote_manager.unsubscribe_quote(stock_code, period)

    def switch_quote_server(self, server_info: dict) -> bool:
        """
        切换行情服务器

        Args:
            server_info: 服务器信息

        Returns:
            切换是否成功
        """
        return self.quote_manager.switch_quote_server(server_info)

    def default_quote_callback(self, datas):
        """默认行情回调函数"""
        logger.debug(f"行情更新: {datas}")

    # 工具方法
    def is_trading_time(self, **kwargs) -> bool:
        """
        验证当前是否为交易时间

        Args:
            **kwargs: 传递给is_trading_time的参数

        Returns:
            是否为交易时间
        """
        return is_trading_time(**kwargs)

    def adjust_stock(self, stock: str) -> str:
        """
        标准化证券代码格式（向后兼容方法）

        Args:
            stock: 股票代码

        Returns:
            标准化后的股票代码
        """
        return adjust_stock_code(stock)

    def _save_order_to_database(self, stock_code: str, order_type: int, order_volume: int,
                               price: float, order_id: int, strategy_name: str, order_remark: str):
        """
        保存委托数据到数据库

        Args:
            stock_code: 股票代码
            order_type: 委托类型
            order_volume: 委托数量
            price: 委托价格
            order_id: 委托ID
            strategy_name: 策略名称
            order_remark: 委托备注
        """
        try:
            from datetime import datetime
            from decimal import Decimal

            order_data = TradeOrderModel(
                account_type=self.account_type,
                account_id=self.account,
                stock_code=stock_code,
                order_id=str(order_id),
                order_time=datetime.now(),
                order_type=order_type,
                order_volume=order_volume,
                order_price=Decimal(str(price)),
                order_status=0,  # 初始状态
                strategy_name=strategy_name,
                order_remark=order_remark
            )

            trading_data_storage.save_trade_order(order_data)
            logger.debug(f"委托数据已保存到数据库: {order_id}")

        except Exception as e:
            logger.error(f"保存委托数据到数据库失败: {str(e)}")

    def _save_balance_to_database(self, asset_data: dict):
        """
        保存资金数据到数据库

        Args:
            asset_data: 资金数据字典
        """
        try:
            from datetime import datetime, date
            from decimal import Decimal

            asset_model = AccountAssetModel(
                account_type=asset_data.get('账号类型', ''),
                account_id=asset_data.get('资金账户', ''),
                cash=Decimal(str(asset_data.get('可用金额', 0))),
                frozen_cash=Decimal(str(asset_data.get('冻结金额', 0))),
                market_value=Decimal(str(asset_data.get('持仓市值', 0))),
                total_asset=Decimal(str(asset_data.get('总资产', 0))),
                record_date=date.today(),
                record_time=datetime.now()
            )

            trading_data_storage.save_account_asset(asset_model)
            logger.debug("资金数据已保存到数据库")

        except Exception as e:
            logger.error(f"保存资金数据到数据库失败: {str(e)}")

    def _save_positions_to_database(self, positions_df: pd.DataFrame):
        """
        保存持仓数据到数据库

        Args:
            positions_df: 持仓数据DataFrame
        """
        try:
            from datetime import datetime, date
            from decimal import Decimal

            for _, row in positions_df.iterrows():
                position_model = PositionModel(
                    account_type=row.get('账号类型', ''),
                    account_id=row.get('资金账号', ''),
                    stock_code=row.get('证券代码', ''),
                    volume=int(row.get('股票余额', 0)),
                    can_use_volume=int(row.get('可用余额', 0)),
                    open_price=Decimal(str(row.get('成本价', 0))),
                    market_value=Decimal(str(row.get('市值', 0))),
                    record_date=date.today(),
                    record_time=datetime.now()
                )

                trading_data_storage.save_position(position_model)

            logger.debug(f"持仓数据已保存到数据库: {len(positions_df)} 条")

        except Exception as e:
            logger.error(f"保存持仓数据到数据库失败: {str(e)}")

    def enable_database_storage(self, enable: bool = True):
        """
        启用或禁用数据库存储

        Args:
            enable: 是否启用数据库存储
        """
        self.enable_database = enable
        logger.info(f"数据库存储已{'启用' if enable else '禁用'}")

    def get_historical_orders(self, start_date=None, end_date=None) -> pd.DataFrame:
        """
        获取历史委托数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            历史委托数据DataFrame
        """
        try:
            return trading_data_query.get_trade_orders(
                account_id=self.account,
                start_date=start_date,
                end_date=end_date
            )
        except Exception as e:
            logger.error(f"查询历史委托数据失败: {str(e)}")
            return pd.DataFrame()

    def get_historical_trades(self, start_date=None, end_date=None) -> pd.DataFrame:
        """
        获取历史成交数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            历史成交数据DataFrame
        """
        try:
            return trading_data_query.get_trade_records(
                account_id=self.account,
                start_date=start_date,
                end_date=end_date
            )
        except Exception as e:
            logger.error(f"查询历史成交数据失败: {str(e)}")
            return pd.DataFrame()

    def get_historical_positions(self, start_date=None, end_date=None) -> pd.DataFrame:
        """
        获取历史持仓数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            历史持仓数据DataFrame
        """
        try:
            return trading_data_query.get_positions(
                account_id=self.account
            )
        except Exception as e:
            logger.error(f"查询历史持仓数据失败: {str(e)}")
            return pd.DataFrame()

    def get_historical_assets(self, start_date=None, end_date=None) -> pd.DataFrame:
        """
        获取历史资金数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            历史资金数据DataFrame
        """
        try:
            return trading_data_query.get_account_assets(
                account_id=self.account,
                start_date=start_date,
                end_date=end_date
            )
        except Exception as e:
            logger.error(f"查询历史资金数据失败: {str(e)}")
            return pd.DataFrame()


# 向后兼容的别名
EasyQMTTrader = QMTTrader
