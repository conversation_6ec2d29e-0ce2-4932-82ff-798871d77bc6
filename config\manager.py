# qmt_trader/config/manager.py
"""
配置管理模块
负责配置文件的加载、保存和管理
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_CONFIG = {
    "trader_path": r"D:/国金QMT交易端模拟/userdata_mini",
    "account": "********",
    "database": {
        "type": "sqlite",
        "path": "qmt_trader.db",
        "enable_storage": True
    }
}


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件名，默认为 config.json
        """
        self.config_file = config_file
        self.config_path = Path(__file__).parent.parent.parent / config_file
        self._config = None
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if self._config is not None:
            return self._config
            
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                    logger.info(f"配置文件加载成功: {self.config_path}")
                    return self._config
        except Exception as e:
            logger.error(f"配置文件加载失败: {str(e)}")

        # 创建默认配置
        logger.info("使用默认配置并创建配置文件")
        self._config = DEFAULT_CONFIG.copy()
        self.save_config(self._config)
        return self._config
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置字典
            
        Returns:
            保存是否成功
        """
        try:
            # 确保目录存在
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            self._config = config
            logger.info(f"配置文件保存成功: {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"配置文件保存失败: {str(e)}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        config = self.load_config()
        return config.get(key, default)
    
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            设置是否成功
        """
        config = self.load_config()
        config[key] = value
        return self.save_config(config)
    
    def update(self, updates: Dict[str, Any]) -> bool:
        """
        批量更新配置
        
        Args:
            updates: 要更新的配置字典
            
        Returns:
            更新是否成功
        """
        config = self.load_config()
        config.update(updates)
        return self.save_config(config)
    
    def reset_to_default(self) -> bool:
        """
        重置为默认配置

        Returns:
            重置是否成功
        """
        return self.save_config(DEFAULT_CONFIG.copy())

    def init_database(self) -> bool:
        """
        初始化数据库

        Returns:
            初始化是否成功
        """
        try:
            config = self.load_config()
            db_config = config.get('database', {})

            if not db_config.get('enable_storage', True):
                logger.info("数据库存储已禁用，跳过初始化")
                return True

            # 导入数据库模块
            from ..database import database_manager, configure_database, init_database

            # 配置数据库
            configure_database(db_config)

            # 初始化数据库表
            init_database()

            logger.info("数据库初始化成功")
            return True

        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            return False

    def get_database_config(self) -> Dict[str, Any]:
        """
        获取数据库配置

        Returns:
            数据库配置字典
        """
        config = self.load_config()
        return config.get('database', DEFAULT_CONFIG['database'].copy())

    def set_database_config(self, db_config: Dict[str, Any]) -> bool:
        """
        设置数据库配置

        Args:
            db_config: 数据库配置字典

        Returns:
            设置是否成功
        """
        try:
            config = self.load_config()
            config['database'] = db_config
            return self.save_config(config)
        except Exception as e:
            logger.error(f"设置数据库配置失败: {str(e)}")
            return False


# 全局配置管理器实例
config_manager = ConfigManager()


def load_config() -> Dict[str, Any]:
    """
    加载配置文件（向后兼容函数）
    
    Returns:
        配置字典
    """
    return config_manager.load_config()


def get_config(key: str, default: Any = None) -> Any:
    """
    获取配置项（便捷函数）
    
    Args:
        key: 配置键
        default: 默认值
        
    Returns:
        配置值
    """
    return config_manager.get(key, default)


def set_config(key: str, value: Any) -> bool:
    """
    设置配置项（便捷函数）
    
    Args:
        key: 配置键
        value: 配置值
        
    Returns:
        设置是否成功
    """
    return config_manager.set(key, value)
