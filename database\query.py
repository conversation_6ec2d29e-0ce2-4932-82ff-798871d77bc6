# qmt_trader/database/query.py
"""
数据查询接口模块
提供数据库数据的查询功能
"""

import logging
import pandas as pd
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Union

from .connection import database_manager

logger = logging.getLogger(__name__)


class MarketDataQuery:
    """行情数据查询类"""
    
    def __init__(self):
        """初始化行情数据查询"""
        self.table_name = 'market_data'
    
    def get_market_data(
        self,
        stock_code: str,
        period: str = '1d',
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        limit: Optional[int] = None
    ) -> pd.DataFrame:
        """
        获取行情数据
        
        Args:
            stock_code: 股票代码
            period: 周期
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制条数
            
        Returns:
            行情数据DataFrame
        """
        try:
            sql = f"""
                SELECT timestamp, open_price, high_price, low_price, close_price, volume, amount
                FROM {self.table_name}
                WHERE stock_code = ? AND period = ?
            """
            params = [stock_code, period]
            
            if start_date:
                sql += " AND timestamp >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND timestamp <= ?"
                params.append(end_date)
            
            sql += " ORDER BY timestamp"
            
            if limit:
                sql += f" LIMIT {limit}"
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, params)
                rows = cursor.fetchall()
            
            if not rows:
                return pd.DataFrame()
            
            # 转换为DataFrame
            data = []
            for row in rows:
                data.append({
                    'timestamp': row[0],
                    'open': float(row[1]) if row[1] else None,
                    'high': float(row[2]) if row[2] else None,
                    'low': float(row[3]) if row[3] else None,
                    'close': float(row[4]) if row[4] else None,
                    'volume': row[5],
                    'amount': float(row[6]) if row[6] else None
                })
            
            df = pd.DataFrame(data)
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            logger.debug(f"查询行情数据: {stock_code} {period}, {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"查询行情数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_latest_price(self, stock_code: str, period: str = '1d') -> Optional[Dict[str, Any]]:
        """
        获取最新价格数据
        
        Args:
            stock_code: 股票代码
            period: 周期
            
        Returns:
            最新价格数据字典
        """
        try:
            sql = f"""
                SELECT timestamp, open_price, high_price, low_price, close_price, volume, amount
                FROM {self.table_name}
                WHERE stock_code = ? AND period = ?
                ORDER BY timestamp DESC
                LIMIT 1
            """
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, (stock_code, period))
                row = cursor.fetchone()
            
            if not row:
                return None
            
            return {
                'timestamp': row[0],
                'open': float(row[1]) if row[1] else None,
                'high': float(row[2]) if row[2] else None,
                'low': float(row[3]) if row[3] else None,
                'close': float(row[4]) if row[4] else None,
                'volume': row[5],
                'amount': float(row[6]) if row[6] else None
            }
            
        except Exception as e:
            logger.error(f"查询最新价格失败: {str(e)}")
            return None
    
    def get_stock_list(self, period: str = '1d') -> List[str]:
        """
        获取已存储的股票列表
        
        Args:
            period: 周期
            
        Returns:
            股票代码列表
        """
        try:
            sql = f"""
                SELECT DISTINCT stock_code
                FROM {self.table_name}
                WHERE period = ?
                ORDER BY stock_code
            """
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, (period,))
                rows = cursor.fetchall()
            
            return [row[0] for row in rows]
            
        except Exception as e:
            logger.error(f"查询股票列表失败: {str(e)}")
            return []
    
    def get_data_range(self, stock_code: str, period: str = '1d') -> Optional[Dict[str, datetime]]:
        """
        获取数据时间范围
        
        Args:
            stock_code: 股票代码
            period: 周期
            
        Returns:
            时间范围字典 {'start': datetime, 'end': datetime}
        """
        try:
            sql = f"""
                SELECT MIN(timestamp) as start_time, MAX(timestamp) as end_time
                FROM {self.table_name}
                WHERE stock_code = ? AND period = ?
            """
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, (stock_code, period))
                row = cursor.fetchone()
            
            if not row or not row[0]:
                return None
            
            return {
                'start': pd.to_datetime(row[0]),
                'end': pd.to_datetime(row[1])
            }
            
        except Exception as e:
            logger.error(f"查询数据范围失败: {str(e)}")
            return None


class TradingDataQuery:
    """交易数据查询类"""
    
    def __init__(self):
        """初始化交易数据查询"""
        pass
    
    def get_trade_orders(
        self,
        account_id: Optional[str] = None,
        stock_code: Optional[str] = None,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        order_status: Optional[int] = None
    ) -> pd.DataFrame:
        """
        查询交易委托
        
        Args:
            account_id: 账户ID
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            order_status: 委托状态
            
        Returns:
            委托数据DataFrame
        """
        try:
            sql = """
                SELECT * FROM trade_orders
                WHERE 1=1
            """
            params = []
            
            if account_id:
                sql += " AND account_id = ?"
                params.append(account_id)
            
            if stock_code:
                sql += " AND stock_code = ?"
                params.append(stock_code)
            
            if start_date:
                sql += " AND order_time >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND order_time <= ?"
                params.append(end_date)
            
            if order_status is not None:
                sql += " AND order_status = ?"
                params.append(order_status)
            
            sql += " ORDER BY order_time DESC"
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, params)
                rows = cursor.fetchall()
            
            if not rows:
                return pd.DataFrame()
            
            # 获取列名
            columns = [description[0] for description in cursor.description]
            
            # 转换为DataFrame
            df = pd.DataFrame(rows, columns=columns)
            
            logger.debug(f"查询委托数据: {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"查询委托数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_trade_records(
        self,
        account_id: Optional[str] = None,
        stock_code: Optional[str] = None,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None
    ) -> pd.DataFrame:
        """
        查询成交记录
        
        Args:
            account_id: 账户ID
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            成交记录DataFrame
        """
        try:
            sql = """
                SELECT * FROM trade_records
                WHERE 1=1
            """
            params = []
            
            if account_id:
                sql += " AND account_id = ?"
                params.append(account_id)
            
            if stock_code:
                sql += " AND stock_code = ?"
                params.append(stock_code)
            
            if start_date:
                sql += " AND trade_time >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND trade_time <= ?"
                params.append(end_date)
            
            sql += " ORDER BY trade_time DESC"
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, params)
                rows = cursor.fetchall()
            
            if not rows:
                return pd.DataFrame()
            
            # 获取列名
            columns = [description[0] for description in cursor.description]
            
            # 转换为DataFrame
            df = pd.DataFrame(rows, columns=columns)
            
            logger.debug(f"查询成交记录: {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"查询成交记录失败: {str(e)}")
            return pd.DataFrame()
    
    def get_positions(
        self,
        account_id: Optional[str] = None,
        stock_code: Optional[str] = None,
        record_date: Optional[Union[str, date]] = None
    ) -> pd.DataFrame:
        """
        查询持仓数据
        
        Args:
            account_id: 账户ID
            stock_code: 股票代码
            record_date: 记录日期
            
        Returns:
            持仓数据DataFrame
        """
        try:
            sql = """
                SELECT * FROM positions
                WHERE 1=1
            """
            params = []
            
            if account_id:
                sql += " AND account_id = ?"
                params.append(account_id)
            
            if stock_code:
                sql += " AND stock_code = ?"
                params.append(stock_code)
            
            if record_date:
                sql += " AND record_date = ?"
                params.append(record_date)
            
            sql += " ORDER BY record_date DESC, stock_code"
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, params)
                rows = cursor.fetchall()
            
            if not rows:
                return pd.DataFrame()
            
            # 获取列名
            columns = [description[0] for description in cursor.description]
            
            # 转换为DataFrame
            df = pd.DataFrame(rows, columns=columns)
            
            logger.debug(f"查询持仓数据: {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"查询持仓数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_account_assets(
        self,
        account_id: Optional[str] = None,
        start_date: Optional[Union[str, date]] = None,
        end_date: Optional[Union[str, date]] = None
    ) -> pd.DataFrame:
        """
        查询账户资金数据
        
        Args:
            account_id: 账户ID
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            资金数据DataFrame
        """
        try:
            sql = """
                SELECT * FROM account_assets
                WHERE 1=1
            """
            params = []
            
            if account_id:
                sql += " AND account_id = ?"
                params.append(account_id)
            
            if start_date:
                sql += " AND record_date >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND record_date <= ?"
                params.append(end_date)
            
            sql += " ORDER BY record_date DESC"
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, params)
                rows = cursor.fetchall()
            
            if not rows:
                return pd.DataFrame()
            
            # 获取列名
            columns = [description[0] for description in cursor.description]
            
            # 转换为DataFrame
            df = pd.DataFrame(rows, columns=columns)
            
            logger.debug(f"查询资金数据: {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"查询资金数据失败: {str(e)}")
            return pd.DataFrame()


# 全局查询实例
market_data_query = MarketDataQuery()
trading_data_query = TradingDataQuery()
