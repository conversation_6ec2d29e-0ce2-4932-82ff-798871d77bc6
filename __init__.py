# qmt_trader/__init__.py
"""
QMT交易器模块化版本

提供完整的量化交易功能，包括：
- 配置管理
- 交易执行
- 行情监控
- 回调处理
- 工具函数

向后兼容原有的 easy_qmt_trader.py 接口
"""

import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# 导入各模块的主要类和函数
from .config import (
    ConfigManager,
    config_manager,
    load_config,
    get_config,
    set_config,
    DEFAULT_CONFIG
)

from .utils import (
    # 常量
    ORDER_TYPE_MAPPING,
    STOCK_EXCHANGE_MAP,
    TRADING_TIME_CONFIG,
    DEFAULT_SLIPPAGE,
    
    # 时间工具
    conv_time,
    is_trading_time,
    get_current_time_info,
    get_trading_sessions,
    
    # 股票工具
    adjust_stock_code,
    get_security_type,
    apply_slippage,
    validate_stock_code,
    get_exchange_info
)

from .callbacks import (
    QMTTraderCallback,
    MyXtQuantTraderCallback  # 向后兼容
)

from .market_data import (
    QuoteManager,
    quote_manager,
    subscribe_quote,
    unsubscribe_quote,
    switch_quote_server
)

from .trader import (
    QMTTrader,
    EasyQMTTrader  # 向后兼容
)

from .database import (
    # 连接管理
    DatabaseManager,
    database_manager,
    get_connection,
    close_connection,
    configure_database,
    init_database,

    # 数据模型
    MarketDataModel,
    TradeOrderModel,
    TradeRecordModel,
    PositionModel,
    AccountAssetModel,

    # 数据存储
    MarketDataStorage,
    TradingDataStorage,
    market_data_storage,
    trading_data_storage,

    # 数据查询
    MarketDataQuery,
    TradingDataQuery,
    market_data_query,
    trading_data_query
)

# 版本信息
__version__ = "2.0.0"
__author__ = "QMT Trader Team"
__description__ = "模块化的QMT量化交易接口"

# 公开的API
__all__ = [
    # 版本信息
    '__version__',
    '__author__',
    '__description__',
    
    # 配置管理
    'ConfigManager',
    'config_manager',
    'load_config',
    'get_config',
    'set_config',
    'DEFAULT_CONFIG',
    
    # 常量
    'ORDER_TYPE_MAPPING',
    'STOCK_EXCHANGE_MAP',
    'TRADING_TIME_CONFIG',
    'DEFAULT_SLIPPAGE',
    
    # 时间工具
    'conv_time',
    'is_trading_time',
    'get_current_time_info',
    'get_trading_sessions',
    
    # 股票工具
    'adjust_stock_code',
    'get_security_type',
    'apply_slippage',
    'validate_stock_code',
    'get_exchange_info',
    
    # 回调处理
    'QMTTraderCallback',
    'MyXtQuantTraderCallback',
    
    # 行情管理
    'QuoteManager',
    'quote_manager',
    'subscribe_quote',
    'unsubscribe_quote',
    'switch_quote_server',
    
    # 核心交易
    'QMTTrader',
    'EasyQMTTrader',

    # 数据库功能
    'DatabaseManager',
    'database_manager',
    'get_connection',
    'close_connection',
    'configure_database',
    'init_database',

    # 数据模型
    'MarketDataModel',
    'TradeOrderModel',
    'TradeRecordModel',
    'PositionModel',
    'AccountAssetModel',

    # 数据存储
    'MarketDataStorage',
    'TradingDataStorage',
    'market_data_storage',
    'trading_data_storage',

    # 数据查询
    'MarketDataQuery',
    'TradingDataQuery',
    'market_data_query',
    'trading_data_query'
]

# 便捷导入（向后兼容）
# 用户可以直接从包根目录导入主要类
from .trader import QMTTrader as Trader
from .trader import EasyQMTTrader

# 设置默认的日志记录器
logger = logging.getLogger('QMT_Trader')

def get_version():
    """获取版本信息"""
    return {
        'version': __version__,
        'author': __author__,
        'description': __description__
    }

def setup_logging(level=logging.INFO, format_str=None):
    """
    设置日志配置
    
    Args:
        level: 日志级别
        format_str: 日志格式字符串
    """
    if format_str is None:
        format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[logging.StreamHandler()],
        force=True  # 强制重新配置
    )
    
    logger.info(f"QMT Trader v{__version__} 日志配置完成")

# 模块初始化时的提示信息
logger.info(f"QMT Trader v{__version__} 模块化版本已加载")
