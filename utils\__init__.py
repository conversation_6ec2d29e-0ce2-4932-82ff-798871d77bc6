# qmt_trader/utils/__init__.py
"""
工具函数模块

提供时间处理、股票代码处理等通用工具函数
"""

from .constants import (
    ORDER_TYPE_MAPPING,
    STOCK_EXCHANGE_MAP,
    SECURITY_TYPE_RULES,
    TRADING_TIME_CONFIG,
    PRICE_PRECISION,
    SLIPPAGE_FACTOR,
    DEFAULT_SLIPPAGE,
    DEFAULT_SESSION_ID_RANGE
)

from .time_utils import (
    conv_time,
    is_trading_time,
    get_current_time_info,
    get_trading_sessions
)

from .stock_utils import (
    adjust_stock_code,
    get_security_type,
    apply_slippage,
    validate_stock_code,
    get_exchange_info
)

__all__ = [
    # 常量
    'ORDER_TYPE_MAPPING',
    'STOCK_EXCHANGE_MAP', 
    'SECURITY_TYPE_RULES',
    'TRADING_TIME_CONFIG',
    'PRICE_PRECISION',
    'SLIP<PERSON>GE_FACTOR',
    'DEFAULT_SLIPPAGE',
    'DEFAULT_SESSION_ID_RANGE',
    
    # 时间工具
    'conv_time',
    'is_trading_time',
    'get_current_time_info',
    'get_trading_sessions',
    
    # 股票工具
    'adjust_stock_code',
    'get_security_type',
    'apply_slippage',
    'validate_stock_code',
    'get_exchange_info'
]
