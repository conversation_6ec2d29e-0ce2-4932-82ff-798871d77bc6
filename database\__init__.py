# qmt_trader/database/__init__.py
"""
数据库存储模块

提供数据库连接管理、数据存储、查询等功能
支持多种数据库类型：SQLite、MySQL、PostgreSQL等
"""

from .connection import (
    DatabaseManager,
    database_manager,
    get_connection,
    close_connection,
    configure_database,
    init_database
)

from .models import (
    MarketDataModel,
    TradeOrderModel,
    TradeRecordModel,
    PositionModel,
    AccountAssetModel
)

from .storage import (
    MarketDataStorage,
    TradingDataStorage,
    market_data_storage,
    trading_data_storage
)

from .query import (
    MarketDataQuery,
    TradingDataQuery,
    market_data_query,
    trading_data_query
)

__all__ = [
    # 连接管理
    'DatabaseManager',
    'database_manager',
    'get_connection',
    'close_connection',
    'configure_database',
    'init_database',
    
    # 数据模型
    'MarketDataModel',
    'TradeOrderModel', 
    'TradeRecordModel',
    'PositionModel',
    'AccountAssetModel',
    
    # 数据存储
    'MarketDataStorage',
    'TradingDataStorage',
    'market_data_storage',
    'trading_data_storage',
    
    # 数据查询
    'MarketDataQuery',
    'TradingDataQuery',
    'market_data_query',
    'trading_data_query'
]
