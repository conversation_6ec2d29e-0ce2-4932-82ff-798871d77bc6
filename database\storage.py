# qmt_trader/database/storage.py
"""
数据存储接口模块
实现行情数据、交易数据的存储功能
"""

import logging
import pandas as pd
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Union
from decimal import Decimal

from .connection import database_manager
from .models import (
    MarketDataModel,
    TradeOrderModel,
    TradeRecordModel,
    PositionModel,
    AccountAssetModel
)

logger = logging.getLogger(__name__)


class MarketDataStorage:
    """行情数据存储类"""
    
    def __init__(self):
        """初始化行情数据存储"""
        self.table_name = 'market_data'
    
    def save_market_data(self, data: Union[MarketDataModel, List[MarketDataModel], pd.DataFrame]) -> bool:
        """
        保存行情数据
        
        Args:
            data: 行情数据，支持单条、批量或DataFrame格式
            
        Returns:
            保存是否成功
        """
        try:
            if isinstance(data, pd.DataFrame):
                return self._save_dataframe(data)
            elif isinstance(data, list):
                return self._save_batch(data)
            elif isinstance(data, MarketDataModel):
                return self._save_single(data)
            else:
                logger.error(f"不支持的数据类型: {type(data)}")
                return False
                
        except Exception as e:
            logger.error(f"保存行情数据失败: {str(e)}")
            return False
    
    def _save_single(self, data: MarketDataModel) -> bool:
        """保存单条行情数据"""
        sql = """
            INSERT OR REPLACE INTO market_data 
            (stock_code, period, timestamp, open_price, high_price, low_price, 
             close_price, volume, amount, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        with database_manager.get_cursor() as cursor:
            cursor.execute(sql, (
                data.stock_code,
                data.period,
                data.timestamp,
                float(data.open_price) if data.open_price else None,
                float(data.high_price) if data.high_price else None,
                float(data.low_price) if data.low_price else None,
                float(data.close_price) if data.close_price else None,
                data.volume,
                float(data.amount) if data.amount else None
            ))
        
        logger.debug(f"保存行情数据: {data.stock_code} {data.period} {data.timestamp}")
        return True
    
    def _save_batch(self, data_list: List[MarketDataModel]) -> bool:
        """批量保存行情数据"""
        if not data_list:
            return True
        
        sql = """
            INSERT OR REPLACE INTO market_data 
            (stock_code, period, timestamp, open_price, high_price, low_price, 
             close_price, volume, amount, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        values = []
        for data in data_list:
            values.append((
                data.stock_code,
                data.period,
                data.timestamp,
                float(data.open_price) if data.open_price else None,
                float(data.high_price) if data.high_price else None,
                float(data.low_price) if data.low_price else None,
                float(data.close_price) if data.close_price else None,
                data.volume,
                float(data.amount) if data.amount else None
            ))
        
        with database_manager.get_cursor() as cursor:
            cursor.executemany(sql, values)
        
        logger.info(f"批量保存行情数据: {len(data_list)} 条")
        return True
    
    def _save_dataframe(self, df: pd.DataFrame) -> bool:
        """保存DataFrame格式的行情数据"""
        # 将DataFrame转换为MarketDataModel列表
        data_list = []
        for _, row in df.iterrows():
            data = MarketDataModel(
                stock_code=row.get('stock_code', ''),
                period=row.get('period', '1d'),
                timestamp=pd.to_datetime(row.name) if isinstance(row.name, (str, pd.Timestamp)) else datetime.now(),
                open_price=Decimal(str(row.get('open', 0))) if pd.notna(row.get('open')) else None,
                high_price=Decimal(str(row.get('high', 0))) if pd.notna(row.get('high')) else None,
                low_price=Decimal(str(row.get('low', 0))) if pd.notna(row.get('low')) else None,
                close_price=Decimal(str(row.get('close', 0))) if pd.notna(row.get('close')) else None,
                volume=int(row.get('volume', 0)) if pd.notna(row.get('volume')) else None,
                amount=Decimal(str(row.get('amount', 0))) if pd.notna(row.get('amount')) else None
            )
            data_list.append(data)
        
        return self._save_batch(data_list)
    
    def save_xtdata_result(self, stock_code: str, period: str, xtdata_result: Dict[str, Any]) -> bool:
        """
        保存xtdata获取的行情数据
        
        Args:
            stock_code: 股票代码
            period: 周期
            xtdata_result: xtdata.get_local_data的返回结果
            
        Returns:
            保存是否成功
        """
        try:
            if not xtdata_result or stock_code not in xtdata_result:
                logger.warning(f"没有找到股票 {stock_code} 的数据")
                return False
            
            stock_data = xtdata_result[stock_code]
            data_list = []
            
            # 获取时间索引
            timestamps = stock_data.index
            
            for timestamp in timestamps:
                row = stock_data.loc[timestamp]
                
                data = MarketDataModel(
                    stock_code=stock_code,
                    period=period,
                    timestamp=pd.to_datetime(timestamp),
                    open_price=Decimal(str(row.get('open', 0))) if pd.notna(row.get('open')) else None,
                    high_price=Decimal(str(row.get('high', 0))) if pd.notna(row.get('high')) else None,
                    low_price=Decimal(str(row.get('low', 0))) if pd.notna(row.get('low')) else None,
                    close_price=Decimal(str(row.get('close', 0))) if pd.notna(row.get('close')) else None,
                    volume=int(row.get('volume', 0)) if pd.notna(row.get('volume')) else None
                )
                data_list.append(data)
            
            return self._save_batch(data_list)
            
        except Exception as e:
            logger.error(f"保存xtdata结果失败: {str(e)}")
            return False


class TradingDataStorage:
    """交易数据存储类"""
    
    def __init__(self):
        """初始化交易数据存储"""
        pass
    
    def save_trade_order(self, order: Union[TradeOrderModel, Dict[str, Any]]) -> bool:
        """
        保存交易委托数据
        
        Args:
            order: 委托数据
            
        Returns:
            保存是否成功
        """
        try:
            if isinstance(order, dict):
                order = TradeOrderModel(**order)
            
            sql = """
                INSERT OR REPLACE INTO trade_orders 
                (account_type, account_id, stock_code, order_id, order_sysid, order_time,
                 order_type, order_volume, price_type, order_price, traded_volume, traded_price,
                 order_status, status_msg, strategy_name, order_remark, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, (
                    order.account_type,
                    order.account_id,
                    order.stock_code,
                    order.order_id,
                    order.order_sysid,
                    order.order_time,
                    order.order_type,
                    order.order_volume,
                    order.price_type,
                    float(order.order_price) if order.order_price else None,
                    order.traded_volume,
                    float(order.traded_price) if order.traded_price else None,
                    order.order_status,
                    order.status_msg,
                    order.strategy_name,
                    order.order_remark
                ))
            
            logger.debug(f"保存委托数据: {order.order_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存委托数据失败: {str(e)}")
            return False
    
    def save_trade_record(self, trade: Union[TradeRecordModel, Dict[str, Any]]) -> bool:
        """
        保存成交记录
        
        Args:
            trade: 成交数据
            
        Returns:
            保存是否成功
        """
        try:
            if isinstance(trade, dict):
                trade = TradeRecordModel(**trade)
            
            sql = """
                INSERT INTO trade_records 
                (account_type, account_id, stock_code, trade_id, order_id, trade_time,
                 trade_volume, trade_price, trade_amount, trade_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, (
                    trade.account_type,
                    trade.account_id,
                    trade.stock_code,
                    trade.trade_id,
                    trade.order_id,
                    trade.trade_time,
                    trade.trade_volume,
                    float(trade.trade_price) if trade.trade_price else None,
                    float(trade.trade_amount) if trade.trade_amount else None,
                    trade.trade_type
                ))
            
            logger.debug(f"保存成交记录: {trade.trade_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存成交记录失败: {str(e)}")
            return False
    
    def save_position(self, position: Union[PositionModel, Dict[str, Any]]) -> bool:
        """
        保存持仓数据
        
        Args:
            position: 持仓数据
            
        Returns:
            保存是否成功
        """
        try:
            if isinstance(position, dict):
                position = PositionModel(**position)
            
            sql = """
                INSERT OR REPLACE INTO positions 
                (account_type, account_id, stock_code, volume, can_use_volume, open_price,
                 market_value, record_date, record_time, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, (
                    position.account_type,
                    position.account_id,
                    position.stock_code,
                    position.volume,
                    position.can_use_volume,
                    float(position.open_price) if position.open_price else None,
                    float(position.market_value) if position.market_value else None,
                    position.record_date,
                    position.record_time
                ))
            
            logger.debug(f"保存持仓数据: {position.account_id} {position.stock_code}")
            return True
            
        except Exception as e:
            logger.error(f"保存持仓数据失败: {str(e)}")
            return False
    
    def save_account_asset(self, asset: Union[AccountAssetModel, Dict[str, Any]]) -> bool:
        """
        保存账户资金数据
        
        Args:
            asset: 资金数据
            
        Returns:
            保存是否成功
        """
        try:
            if isinstance(asset, dict):
                asset = AccountAssetModel(**asset)
            
            sql = """
                INSERT OR REPLACE INTO account_assets 
                (account_type, account_id, cash, frozen_cash, market_value, total_asset,
                 record_date, record_time, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            with database_manager.get_cursor() as cursor:
                cursor.execute(sql, (
                    asset.account_type,
                    asset.account_id,
                    float(asset.cash) if asset.cash else None,
                    float(asset.frozen_cash) if asset.frozen_cash else None,
                    float(asset.market_value) if asset.market_value else None,
                    float(asset.total_asset) if asset.total_asset else None,
                    asset.record_date,
                    asset.record_time
                ))
            
            logger.debug(f"保存资金数据: {asset.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存资金数据失败: {str(e)}")
            return False


# 全局存储实例
market_data_storage = MarketDataStorage()
trading_data_storage = TradingDataStorage()
