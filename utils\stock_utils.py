# qmt_trader/utils/stock_utils.py
"""
股票代码处理工具模块
提供股票代码标准化和证券类型识别功能
"""

import logging
from typing import Union
from .constants import STOCK_EXCHANGE_MAP, SECURITY_TYPE_RULES, SLIPPAGE_FACTOR, PRICE_PRECISION

logger = logging.getLogger(__name__)


def adjust_stock_code(stock: str) -> str:
    """
    标准化证券代码格式
    
    Args:
        stock: 原始股票代码
        
    Returns:
        标准化后的股票代码（包含交易所后缀）
        
    Example:
        adjust_stock_code('000001') -> '000001.SZ'
        adjust_stock_code('600000') -> '600000.SH'
        adjust_stock_code('000001.SZ') -> '000001.SZ'
    """
    if not stock:
        logger.warning("股票代码为空")
        return ""
    
    stock = stock.strip()
    
    # 如果已有后缀，直接返回大写格式
    if '.' in stock:
        return stock.upper()

    # 根据前缀判断交易所
    if len(stock) >= 1:
        prefix = stock[:2] if len(stock) >= 2 else stock[0]
        suffix = STOCK_EXCHANGE_MAP.get(prefix, '.SZ')  # 默认深市
        result = f"{stock}{suffix}"
        logger.debug(f"股票代码标准化: {stock} -> {result}")
        return result
    
    logger.warning(f"无法识别的股票代码格式: {stock}")
    return f"{stock}.SZ"  # 默认深市


def get_security_type(stock: str) -> str:
    """
    获取证券类型
    
    Args:
        stock: 股票代码
        
    Returns:
        证券类型: 'stock', 'bond', 'fund'
        
    Example:
        get_security_type('000001.SZ') -> 'stock'
        get_security_type('110001.SH') -> 'bond'
        get_security_type('510050.SH') -> 'fund'
    """
    if not stock:
        return 'stock'
    
    # 标准化股票代码
    stock = adjust_stock_code(stock)
    
    # 提取代码部分（去掉交易所后缀）
    code = stock.split('.')[0] if '.' in stock else stock
    
    # 检查债券
    bond_rules = SECURITY_TYPE_RULES['bond']
    if (code[:3] in bond_rules['prefixes_3'] or 
        code[:2] in bond_rules['prefixes_2']):
        return 'bond'
    
    # 检查基金
    fund_rules = SECURITY_TYPE_RULES['fund']
    if (code[:3] in fund_rules['prefixes_3'] or 
        code[:2] in fund_rules['prefixes_2']):
        return 'fund'
    
    # 默认为股票
    return 'stock'


def apply_slippage(stock: str, price: float, trade_type: Union[str, int], slippage: float) -> float:
    """
    应用滑点调整
    
    Args:
        stock: 股票代码
        price: 原始价格
        trade_type: 交易类型 ('buy', 'sell' 或对应的常量)
        slippage: 滑点大小
        
    Returns:
        调整后的价格
    """
    if price <= 0:
        logger.warning(f"价格无效: {price}")
        return price
    
    security_type = get_security_type(stock)
    adjusted_price = price
    
    # 获取滑点系数
    factor = SLIPPAGE_FACTOR.get(security_type.upper(), SLIPPAGE_FACTOR['STOCK'])
    actual_slippage = slippage * factor
    
    # 根据交易类型调整价格
    from xtquant import xtconstant
    if trade_type in ['buy', xtconstant.STOCK_BUY]:
        adjusted_price += actual_slippage
    else:  # sell
        adjusted_price -= actual_slippage
    
    # 确保价格不为负
    adjusted_price = max(adjusted_price, 0.001)
    
    # 根据证券类型保留精度
    precision = PRICE_PRECISION.get(security_type.upper(), PRICE_PRECISION['STOCK'])
    adjusted_price = round(adjusted_price, precision)
    
    logger.debug(f"滑点调整: {stock} 类型={security_type} 原价={price} 调整后={adjusted_price}")
    return adjusted_price


def validate_stock_code(stock: str) -> bool:
    """
    验证股票代码格式是否正确
    
    Args:
        stock: 股票代码
        
    Returns:
        是否为有效的股票代码格式
    """
    if not stock or not isinstance(stock, str):
        return False
    
    stock = stock.strip()
    
    # 检查是否包含交易所后缀
    if '.' in stock:
        code, exchange = stock.split('.', 1)
        if exchange.upper() not in ['.SH', '.SZ', '.BJ']:
            return False
    else:
        code = stock
    
    # 检查代码长度和格式
    if len(code) != 6:
        return False
    
    if not code.isdigit():
        return False
    
    return True


def get_exchange_info(stock: str) -> dict:
    """
    获取股票所属交易所信息
    
    Args:
        stock: 股票代码
        
    Returns:
        交易所信息字典
    """
    stock = adjust_stock_code(stock)
    
    if '.SH' in stock:
        return {
            'exchange': 'SH',
            'name': '上海证券交易所',
            'english_name': 'Shanghai Stock Exchange'
        }
    elif '.SZ' in stock:
        return {
            'exchange': 'SZ', 
            'name': '深圳证券交易所',
            'english_name': 'Shenzhen Stock Exchange'
        }
    elif '.BJ' in stock:
        return {
            'exchange': 'BJ',
            'name': '北京证券交易所', 
            'english_name': 'Beijing Stock Exchange'
        }
    else:
        return {
            'exchange': 'UNKNOWN',
            'name': '未知交易所',
            'english_name': 'Unknown Exchange'
        }
